# CD vs 数字

## Q1

这两个群友到底在吵什么？请你详细分析总结和展开讨论他们的论点：

sky82813, [Jun 9, 2025 at 6:44 PM]

数据男听歌都是这样，各种参数各种数据



sky82813, [Jun 9, 2025 at 6:44 PM] 

数据男比较不好听，理工男比较好听点 



TheM14, [Jun 9, 2025 at 6:44 PM] 

没事，声学和电学发展也靠的理工男 



sky82813, [Jun 9, 2025 at 6:45 PM] 

20块钱就能验证的效果 



TheM14, [Jun 9, 2025 at 6:46 PM] 

不多说了，等等又可以吵一整晚了 



sky82813, [Jun 9, 2025 at 6:47 PM] 

有多便宜的碟就买多便宜的碟，正版cd-iso-最渣的刻录机+最大速度刻录进碟片A-生成iso-继续最渣的刻录机+最大速度刻录进碟片B，周而复始，然后用刻录了了10遍20遍的碟片x和正版母cd对比就好了 



sky82813, [Jun 9, 2025 at 6:47 PM] 

数据不会错的，声音就是二码事了 



TheM14, [Jun 9, 2025 at 6:49 PM] 

你这意思就类似于，你从AppleMusic下和我从AppleMusic下的同一首歌有差别，即使hash完全一样 



sky82813, [Jun 9, 2025 at 6:49 PM] 

你这是偷换概念 



TheM14, [Jun 9, 2025 at 6:50 PM] 

不都一样嘛，数据都是一样的 



sky82813, [Jun 9, 2025 at 6:50 PM] 

你用上面的办法写入光碟之后再去对比原m4a试试 



TheM14, [Jun 9, 2025 at 6:50 PM] 

可以一模一样啊 



Andrew arey002, [Jun 9, 2025 at 6:50 PM] 

不就是像注塑一样用模具压制出来的吗？😂 



sky82813, [Jun 9, 2025 at 6:51 PM] 

数据一样，声音就二码事 



Andrew arey002, [Jun 9, 2025 at 6:51 PM] 

其实都是数字音乐 



TheM14, [Jun 9, 2025 at 6:51 PM] 

你是不是没对比去除空采样的数据 



TheM14, [Jun 9, 2025 at 6:51 PM] 

按你说的，你从流媒体下的和我从流媒体下的虽然数据一样，但是声音是两码事 



root@bibichanmarco🧑‍💻:~#, [Jun 9, 2025 at 6:51 PM] 

我家用TCL的Google TV 



TheM14, [Jun 9, 2025 at 6:52 PM] 

因为cdn不一样，中间经过了多少次数据传输也不一样 



TheM14, [Jun 9, 2025 at 6:52 PM] 

就类似于碟片刻录了几次 



sky82813, [Jun 9, 2025 at 6:52 PM] 

用上面的方法刻录进碟片他就不一样了，单纯的数字文件复制来复制去是没有区别 



Andrew arey002, [Jun 9, 2025 at 6:52 PM] 

我懒得喷你🤣 



sky82813, [Jun 9, 2025 at 6:53 PM] 

刻录进去要经过纠错，抖动什么的差异化 



Andrew arey002, [Jun 9, 2025 at 6:53 PM] 

数据一样 声音还有啥区别 



TheM14, [Jun 9, 2025 at 6:53 PM] 

所以你看过CD的红皮书吗 



TheM14, [Jun 9, 2025 at 6:53 PM] 

了解c1和c2纠错机制吗 



sky82813, [Jun 9, 2025 at 6:54 PM] 

所以花几十块就能验证的事情不去实践，一直盯着数据看 



TheM14, [Jun 9, 2025 at 6:54 PM] 

我也弄过刻录碟啊，两次抓出来的数据一模一样，我为什么还要继续下去？浪费时间？ 



sky82813, [Jun 9, 2025 at 6:55 PM] 

声音能一样那就有鬼了 



TheM14, [Jun 9, 2025 at 6:55 PM] 

原版碟和第一次刻录碟抓出来的数据一模一样，说明两者的误差都在c1和c2允许的误码范围内，那得到了a和b两个完全一样的文件，那我后续用a继续刻录还是用b继续刻录有区别吗？ 



sky82813, [Jun 9, 2025 at 6:56 PM] 

一张2毛的碟，买10张20张去实践一下 



TheM14, [Jun 9, 2025 at 6:56 PM] 

所以你告诉我还要继续刻录的理由嘛 



TheM14, [Jun 9, 2025 at 6:57 PM] 

两个一模一样的文件，刻录出来的数据会有差？ 



sky82813, [Jun 9, 2025 at 6:57 PM] 

结论就是数据一样，声音可以不同 



TheM14, [Jun 9, 2025 at 6:57 PM] 

你的结论是怎么测出来的 



TheM14, [Jun 9, 2025 at 6:57 PM] 

有什么可观测的数据吗 



sky82813, [Jun 9, 2025 at 6:57 PM] 

很明显的差异的 



TheM14, [Jun 9, 2025 at 6:57 PM] 

还是说只是你觉得，你听起来感觉是这样子的 



sky82813, [Jun 9, 2025 at 6:57 PM] 

有毛刺感 



sky82813, [Jun 9, 2025 at 6:57 PM] 

很明显的听感差异 



TheM14, [Jun 9, 2025 at 6:58 PM] 

什么是毛刺感 



TheM14, [Jun 9, 2025 at 6:58 PM] 

具体说说在回放信号中发生了什么 



sky82813, [Jun 9, 2025 at 6:58 PM] 

刺刺的感觉 



TheM14, [Jun 9, 2025 at 6:58 PM] 

什么差异造成了这个毛刺感 



sky82813, [Jun 9, 2025 at 6:58 PM] 

波形不顺滑导致 



TheM14, [Jun 9, 2025 at 6:58 PM] 

为什么波形不顺滑 



sky82813, [Jun 9, 2025 at 6:59 PM] 

抖动误差导致描点差异 



TheM14, [Jun 9, 2025 at 6:59 PM] 

一模一样的数据为什么还原出来的波形一个顺滑一个不顺滑 



TheM14, [Jun 9, 2025 at 6:59 PM] 

抖动误差是在哪里引入的 



sky82813, [Jun 9, 2025 at 6:59 PM] 

刻录的越多遍，差异越多 



TheM14, [Jun 9, 2025 at 7:00 PM] 

数字文件存储的时候不存在时域信息，这个误差是怎么来的 



sky82813, [Jun 9, 2025 at 7:00 PM] 

读取数据-内存-cpu->写入数据这些都不用时间啊 



TheM14, [Jun 9, 2025 at 7:00 PM] 

那和我数字文件有什么关系 



sky82813, [Jun 9, 2025 at 7:01 PM] 

问题来了，数字文件对二进制排序很宽松，波形就严格 



TheM14, [Jun 9, 2025 at 7:01 PM] 

数字文件对二进制的排列很宽松是如何定义的 



TheM14, [Jun 9, 2025 at 7:02 PM] 

我和你下载的流媒体的文件排列就是一样宽松或者严格的吗 



sky82813, [Jun 9, 2025 at 7:02 PM] 

0间隔0.05ms 1 二进制他还是认01 
还原波形的话 这里就差了0.05 



TheM14, [Jun 9, 2025 at 7:02 PM] 

数字文件里面哪里存储了这些信息 



sky82813, [Jun 9, 2025 at 7:03 PM] 

又来偷换概念，你下载的文件并没有写入cd而造成时基误差 



TheM14, [Jun 9, 2025 at 7:03 PM] 

那我还说网络传输也有时基误差呢 



sky82813, [Jun 9, 2025 at 7:04 PM] 

不会 



TheM14, [Jun 9, 2025 at 7:04 PM] 

网络传输有纠错码纠错，CD也有啊 



sky82813, [Jun 9, 2025 at 7:04 PM] 

网络传输哪来的纠错 



TheM14, [Jun 9, 2025 at 7:04 PM] 

？ 



sky82813, [Jun 9, 2025 at 7:05 PM] 

网络再烂无非就是缓冲数据进内存，数据写满够播放了再从内存调出 



TheM14, [Jun 9, 2025 at 7:05 PM] 

那CD不也一样 



TheM14, [Jun 9, 2025 at 7:06 PM] 

你要是把CD当CD-R来看，那你觉得在正常情况下会不会出问题？ 



sky82813, [Jun 9, 2025 at 7:06 PM] 

你网络播放相当于cd一次刻录，而没有周而复制 



TheM14, [Jun 9, 2025 at 7:07 PM] 

那就拿bt来说嘛，bt是不是相当于多次刻录了？ 



TheM14, [Jun 9, 2025 at 7:07 PM] 

热门的bt种子随随便便几千人下载 



sky82813, [Jun 9, 2025 at 7:07 PM] 

按你的理论，Dcs的录音室顶级设备时钟分体三件套就是个智商税了 



TheM14, [Jun 9, 2025 at 7:07 PM] 

我还真觉得就是智商税 



TheM14, [Jun 9, 2025 at 7:08 PM] 

而且这个时钟和你数字文件又没关系 



sky82813, [Jun 9, 2025 at 7:08 PM] 

对数字文件没太大影响，对音频文件重生成影响大了 



sky82813, [Jun 9, 2025 at 7:09 PM] 

顶级制作人的智商不会比你们这些理工男傻 



TheM14, [Jun 9, 2025 at 7:10 PM] 

那是涉及模拟数字转换的时候才可能有区别 



TheM14, [Jun 9, 2025 at 7:10 PM] 

而且还是抖动巨大的时候 



sky82813, [Jun 9, 2025 at 7:11 PM] 

因为你到现在还不理解 01 VS 0  (间隔时间)   1 对音频的影响 



TheM14, [Jun 9, 2025 at 7:11 PM] 

因为就没影响 



TheM14, [Jun 9, 2025 at 7:11 PM] 

数字文件里面哪有间隔多久多久的说法 



TheM14, [Jun 9, 2025 at 7:12 PM] 

要是真有，那这个肯定可以被我们提前探测到 



TheM14, [Jun 9, 2025 at 7:12 PM] 

那这些数据到底存储在哪了 



sky82813, [Jun 9, 2025 at 7:12 PM] 

01 VS 0  (间隔时间)   1 在二进制数据中是合理，音频重生波形时候画曲线就会产生偏差 



TheM14, [Jun 9, 2025 at 7:12 PM] 

所以你这个间隔时间存储在哪了 



TheM14, [Jun 9, 2025 at 7:12 PM] 

你能给我们导出来看看吗 



sky82813, [Jun 9, 2025 at 7:13 PM] 

刻录进cd凹槽的间隔 



TheM14, [Jun 9, 2025 at 7:13 PM] 

那有纠错码纠错啊 



TheM14, [Jun 9, 2025 at 7:13 PM] 

而且现在的光驱也一样有缓存啊 



sky82813, [Jun 9, 2025 at 7:13 PM] 

最后建议花点微不足道的小钱去验证一下 



sky82813, [Jun 9, 2025 at 7:14 PM] 

甚至更极端，不生成iso 直接对刻 



TheM14, [Jun 9, 2025 at 7:15 PM] 

你对光驱的偏移值进行校准了吗 



TheM14, [Jun 9, 2025 at 7:16 PM] 

直接对刻会产生多少个采样的偏移？ 



sky82813, [Jun 9, 2025 at 7:16 PM] 

最后的问题是数据一样，声音不同 



TheM14, [Jun 9, 2025 at 7:16 PM] 

所以数据一样声音不同是你臆想出来的罢了 



sky82813, [Jun 9, 2025 at 7:17 PM] 

你理论再多再结合上面说的方法去应证一下嘛 



TheM14, [Jun 9, 2025 at 7:17 PM] 

你都说数据一样了 



TheM14, [Jun 9, 2025 at 7:17 PM] 

我还有什么验证的必要 



TheM14, [Jun 9, 2025 at 7:17 PM] 

我把这文件反复在两个硬盘复制几百次不也一样 



sky82813, [Jun 9, 2025 at 7:18 PM] 

你这话说的正版cd和90年代盗版n遍的cd声音能一样似的 



TheM14, [Jun 9, 2025 at 7:19 PM] 

那个时代刻录的刻录碟能和现刻的相提并论吗 



sky82813, [Jun 9, 2025 at 7:20 PM] 

数据一样啊 



TheM14, [Jun 9, 2025 at 7:20 PM] 

那数据一样声音就一样啊 



sky82813, [Jun 9, 2025 at 7:20 PM] 

他二进制数据又没有问题 



TheM14, [Jun 9, 2025 at 7:20 PM] 

没问题更好了 



sky82813, [Jun 9, 2025 at 7:20 PM] 

你这话说的正版cd和90年代盗版n遍的cd声音能一样似的 



TheM14, [Jun 9, 2025 at 7:20 PM] 

保证数据一样声音就一样啊 



sky82813, [Jun 9, 2025 at 7:21 PM] 

你还是花几十块钱自己验证吧 



TheM14, [Jun 9, 2025 at 7:22 PM] 

我再问你一句，你做abx盲听了吗 



sky82813, [Jun 9, 2025 at 7:22 PM] 

我试过刻录20几遍再和原cd对比，声音几乎一听就能分出 



TheM14, [Jun 9, 2025 at 7:23 PM] 

你做盲听了吗 



sky82813, [Jun 9, 2025 at 7:23 PM] 

对啊 



TheM14, [Jun 9, 2025 at 7:23 PM] 

你有盲听插件的报告吗 



sky82813, [Jun 9, 2025 at 7:23 PM] 

就是盲听，一个老大哥放的，我听，我放，他听，都能识别出来 



TheM14, [Jun 9, 2025 at 7:28 PM] 

你是用CD机听的还是抓轨文件听的
